# 视频最后一帧提取工具

基于Python + FFmpeg的视频最后一帧提取工具，支持多种视频格式和输出格式。

## 功能特点

- 🎬 支持多种视频格式（MP4, AVI, MOV, MKV, WMV, FLV等）
- 🖼️ 支持多种输出图片格式（PNG, JPG, BMP, TIFF等）
- ⚡ 高效的最后一帧提取算法
- 📁 支持批量处理
- 🛠️ 自动检测FFmpeg路径
- 💻 跨平台支持（Windows, Linux, macOS）

## 环境要求

1. **Python 3.6+**
2. **FFmpeg** - 需要安装并添加到系统PATH中

### FFmpeg安装

#### Windows
1. 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载
2. 解压到任意目录（如 `C:\ffmpeg`）
3. 将 `C:\ffmpeg\bin` 添加到系统PATH环境变量

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
```

#### macOS
```bash
brew install ffmpeg
```

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
# 提取最后一帧，自动生成文件名
python get_last_frame.py video.mp4

# 指定输出文件
python get_last_frame.py video.mp4 -o last_frame.png

# 指定输出格式
python get_last_frame.py video.mp4 -f jpg

# 完整参数
python get_last_frame.py video.mp4 -o output/last_frame.jpg -f jpg
```

#### Windows批处理文件
```cmd
# 双击运行或命令行调用
extract_last_frame.bat video.mp4
extract_last_frame.bat video.mp4 last_frame.png
extract_last_frame.bat video.mp4 last_frame.jpg jpg
```

### 2. Python代码中使用

```python
from get_last_frame import VideoLastFrameExtractor

# 创建提取器实例
extractor = VideoLastFrameExtractor()

# 提取最后一帧
output_path = extractor.extract_last_frame('video.mp4')
print(f"最后一帧已保存到: {output_path}")

# 自定义输出
output_path = extractor.extract_last_frame(
    'video.mp4', 
    'custom_output.jpg', 
    'jpg'
)
```

### 3. 批量处理示例

```python
# 运行示例脚本
python example_usage.py
```

## 文件说明

- `get_last_frame.py` - 主要的提取工具类和命令行接口
- `example_usage.py` - 使用示例和批量处理演示
- `extract_last_frame.bat` - Windows批处理脚本
- `README.md` - 说明文档

## 支持的格式

### 输入视频格式
- MP4, AVI, MOV, MKV, WMV, FLV, 3GP, WEBM等

### 输出图片格式
- PNG (默认)
- JPG/JPEG
- BMP
- TIFF

## 错误处理

工具包含完善的错误处理机制：

- 自动检测FFmpeg安装
- 验证输入文件存在性
- 处理视频时长获取失败的情况
- 提供详细的错误信息

## 技术原理

1. **时长获取**: 使用FFmpeg获取视频精确时长
2. **帧提取**: 通过时间点定位或反向查找获取最后一帧
3. **格式转换**: 支持多种输出格式的自动转换

## 常见问题

### Q: 提示"未找到FFmpeg"
A: 请确保已安装FFmpeg并添加到系统PATH环境变量中

### Q: 某些视频无法获取最后一帧
A: 工具会自动尝试备用方法，如果仍然失败，请检查视频文件是否损坏

### Q: 输出图片质量如何调整
A: 目前使用FFmpeg默认质量设置，如需调整可修改代码中的FFmpeg参数

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
