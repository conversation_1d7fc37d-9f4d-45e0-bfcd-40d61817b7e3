#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取视频第一帧并保存的工具
基于Python + FFmpeg实现
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


class VideoFirstFrameExtractor:
    """视频第一帧提取器"""
    
    def __init__(self):
        self.ffmpeg_path = self._find_ffmpeg()
    
    def _find_ffmpeg(self):
        """查找FFmpeg可执行文件路径"""
        # 尝试在系统PATH中查找ffmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return 'ffmpeg'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # 如果系统PATH中没有，尝试常见的安装路径
        common_paths = [
            'ffmpeg.exe',
            'C:/ffmpeg/bin/ffmpeg.exe',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        raise FileNotFoundError("未找到FFmpeg，请确保已安装FFmpeg并添加到系统PATH中")
    
    def get_video_info(self, video_path):
        """获取视频基本信息"""
        cmd = [
            self.ffmpeg_path,
            '-i', video_path,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30, encoding='utf-8', errors='ignore')
            info = {}
            
            # 从stderr中解析视频信息
            for line in result.stderr.split('\n'):
                if 'Duration:' in line:
                    duration_str = line.split('Duration:')[1].split(',')[0].strip()
                    # 解析时:分:秒.毫秒格式
                    time_parts = duration_str.split(':')
                    hours = float(time_parts[0])
                    minutes = float(time_parts[1])
                    seconds = float(time_parts[2])
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                    info['duration'] = total_seconds
                
                if 'Video:' in line:
                    # 解析分辨率信息
                    if 'x' in line:
                        parts = line.split()
                        for part in parts:
                            if 'x' in part and part.replace('x', '').replace(',', '').isdigit():
                                resolution = part.rstrip(',')
                                info['resolution'] = resolution
                                break
                    
                    # 解析帧率信息
                    if 'fps' in line:
                        parts = line.split()
                        for i, part in enumerate(parts):
                            if 'fps' in part and i > 0:
                                try:
                                    fps = float(parts[i-1])
                                    info['fps'] = fps
                                except ValueError:
                                    pass
                                break
            
            return info
            
        except subprocess.TimeoutExpired:
            raise Exception("获取视频信息超时")
        except Exception as e:
            raise Exception(f"获取视频信息失败: {str(e)}")
    
    def extract_first_frame(self, video_path, output_path=None, image_format='png', seek_time=0.0):
        """
        提取视频第一帧
        
        Args:
            video_path (str): 输入视频文件路径
            output_path (str, optional): 输出图片路径，如果不指定则自动生成
            image_format (str): 输出图片格式，默认为png
            seek_time (float): 跳转到指定时间点（秒），默认为0.0（第一帧）
        
        Returns:
            str: 输出图片的路径
        """
        # 验证输入文件
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 生成输出路径
        if output_path is None:
            video_name = Path(video_path).stem
            if seek_time == 0.0:
                output_path = f"{video_name}_first_frame.{image_format}"
            else:
                output_path = f"{video_name}_frame_{seek_time:.1f}s.{image_format}"
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"正在处理视频: {video_path}")
        print(f"输出路径: {output_path}")
        print(f"提取时间点: {seek_time:.1f}秒")
        
        # 获取视频信息
        try:
            video_info = self.get_video_info(video_path)
            if 'duration' in video_info:
                print(f"视频时长: {video_info['duration']:.2f}秒")
            if 'resolution' in video_info:
                print(f"视频分辨率: {video_info['resolution']}")
            if 'fps' in video_info:
                print(f"视频帧率: {video_info['fps']:.2f} fps")
                
        except Exception as e:
            print(f"警告: 无法获取视频详细信息: {str(e)}")
        
        # 构建FFmpeg命令
        cmd = [
            self.ffmpeg_path,
            '-ss', str(seek_time),  # 跳转到指定时间点
            '-i', video_path,       # 输入文件
            '-vframes', '1',        # 只提取一帧
            '-q:v', '2',           # 高质量设置
            '-y',                   # 覆盖输出文件
            output_path
        ]
        
        try:
            print("正在提取帧...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60, encoding='utf-8', errors='ignore')
            
            if result.returncode == 0:
                if os.path.exists(output_path):
                    file_size = os.path.getsize(output_path)
                    print(f"✓ 成功提取帧: {output_path} ({file_size} bytes)")
                    return output_path
                else:
                    raise Exception("FFmpeg执行成功但未生成输出文件")
            else:
                raise Exception(f"FFmpeg执行失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("提取帧超时")
        except Exception as e:
            raise Exception(f"提取帧失败: {str(e)}")
    
    def extract_multiple_frames(self, video_path, frame_times, output_dir=None, image_format='png'):
        """
        提取多个时间点的帧
        
        Args:
            video_path (str): 输入视频文件路径
            frame_times (list): 时间点列表（秒）
            output_dir (str, optional): 输出目录，如果不指定则使用当前目录
            image_format (str): 输出图片格式，默认为png
        
        Returns:
            list: 输出图片路径列表
        """
        if output_dir is None:
            output_dir = "."
        
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        video_name = Path(video_path).stem
        output_paths = []
        
        print(f"批量提取 {len(frame_times)} 个帧...")
        
        for i, time_point in enumerate(frame_times):
            try:
                output_filename = f"{video_name}_frame_{i+1:03d}_{time_point:.1f}s.{image_format}"
                output_path = os.path.join(output_dir, output_filename)
                
                result_path = self.extract_first_frame(
                    video_path, 
                    output_path, 
                    image_format, 
                    time_point
                )
                output_paths.append(result_path)
                
            except Exception as e:
                print(f"✗ 提取第 {i+1} 帧失败 (时间点 {time_point}s): {e}")
        
        print(f"批量提取完成: {len(output_paths)}/{len(frame_times)} 个帧成功")
        return output_paths


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取视频第一帧或指定时间点的帧并保存')
    parser.add_argument('video_path', help='输入视频文件路径')
    parser.add_argument('-o', '--output', help='输出图片路径（可选）')
    parser.add_argument('-f', '--format', default='png', 
                       choices=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
                       help='输出图片格式（默认: png）')
    parser.add_argument('-t', '--time', type=float, default=0.0,
                       help='提取指定时间点的帧（秒），默认为0.0（第一帧）')
    parser.add_argument('--multiple', nargs='+', type=float,
                       help='提取多个时间点的帧（秒），例如: --multiple 0 1.5 3.0 5.2')
    parser.add_argument('--output-dir', help='批量提取时的输出目录')
    
    args = parser.parse_args()
    
    try:
        extractor = VideoFirstFrameExtractor()
        
        if args.multiple:
            # 批量提取多个帧
            output_paths = extractor.extract_multiple_frames(
                args.video_path,
                args.multiple,
                args.output_dir,
                args.format
            )
            print(f"\n✓ 批量处理完成！共提取 {len(output_paths)} 个帧")
            for path in output_paths:
                print(f"  - {path}")
        else:
            # 提取单个帧
            output_path = extractor.extract_first_frame(
                args.video_path, 
                args.output, 
                args.format,
                args.time
            )
            if args.time == 0.0:
                print(f"\n✓ 处理完成！第一帧已保存到: {output_path}")
            else:
                print(f"\n✓ 处理完成！{args.time}秒处的帧已保存到: {output_path}")
        
    except Exception as e:
        print(f"\n✗ 错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
