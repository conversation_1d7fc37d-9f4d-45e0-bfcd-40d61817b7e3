#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证视频第一帧提取工具的所有功能
"""

import os
import sys
from get_first_frame import VideoFirstFrameExtractor


def test_first_frame_extraction():
    """测试第一帧提取功能"""
    print("=== 测试第一帧提取功能 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    try:
        # 测试基本第一帧提取
        output_path = extractor.extract_first_frame(video_file)
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"✅ 第一帧提取成功: {output_path} ({file_size:,} bytes)")
        else:
            print("❌ 第一帧提取失败：输出文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 第一帧提取异常: {e}")
        return False
    
    return True


def test_specific_time_extraction():
    """测试指定时间点帧提取"""
    print("\n=== 测试指定时间点帧提取 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    # 测试不同时间点
    test_times = [0.0, 1.0, 2.5]
    success_count = 0
    
    for time_point in test_times:
        try:
            output_path = f"test_frame_{time_point:.1f}s.png"
            result = extractor.extract_first_frame(video_file, output_path, "png", time_point)
            
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✅ {time_point}秒帧提取成功: {result} ({file_size:,} bytes)")
                success_count += 1
            else:
                print(f"❌ {time_point}秒帧提取失败：文件不存在")
                
        except Exception as e:
            print(f"❌ {time_point}秒帧提取异常: {e}")
    
    return success_count == len(test_times)


def test_multiple_formats():
    """测试多种输出格式"""
    print("\n=== 测试多种输出格式 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    # 测试不同格式
    formats = ['png', 'jpg', 'bmp']
    success_count = 0
    
    for fmt in formats:
        try:
            output_path = f"test_first_frame.{fmt}"
            result = extractor.extract_first_frame(video_file, output_path, fmt)
            
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✅ {fmt.upper()} 格式成功: {result} ({file_size:,} bytes)")
                success_count += 1
            else:
                print(f"❌ {fmt.upper()} 格式失败：文件不存在")
                
        except Exception as e:
            print(f"❌ {fmt.upper()} 格式异常: {e}")
    
    return success_count == len(formats)


def test_multiple_frames():
    """测试批量帧提取"""
    print("\n=== 测试批量帧提取 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    try:
        # 创建测试输出目录
        test_dir = "test_multiple_frames"
        if not os.path.exists(test_dir):
            os.makedirs(test_dir)
        
        # 测试批量提取
        frame_times = [0.0, 1.0, 2.0, 3.0]
        output_paths = extractor.extract_multiple_frames(
            video_file, frame_times, test_dir, "png"
        )
        
        success_count = 0
        for path in output_paths:
            if os.path.exists(path):
                file_size = os.path.getsize(path)
                print(f"✅ 批量提取成功: {path} ({file_size:,} bytes)")
                success_count += 1
            else:
                print(f"❌ 批量提取失败: {path}")
        
        return success_count == len(frame_times)
        
    except Exception as e:
        print(f"❌ 批量提取异常: {e}")
        return False


def test_video_info():
    """测试视频信息获取"""
    print("\n=== 测试视频信息获取 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    try:
        video_info = extractor.get_video_info(video_file)
        
        required_keys = ['duration']
        success = True
        
        for key in required_keys:
            if key in video_info:
                print(f"✅ {key}: {video_info[key]}")
            else:
                print(f"❌ 缺少 {key} 信息")
                success = False
        
        # 检查可选信息
        optional_keys = ['resolution', 'fps']
        for key in optional_keys:
            if key in video_info:
                print(f"✅ {key}: {video_info[key]}")
            else:
                print(f"⚠️  可选信息 {key} 未获取到")
        
        return success
        
    except Exception as e:
        print(f"❌ 视频信息获取异常: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    extractor = VideoFirstFrameExtractor()
    
    # 测试不存在的文件
    try:
        extractor.extract_first_frame("nonexistent_video.mp4")
        print("❌ 错误处理失败：应该抛出异常")
        return False
    except FileNotFoundError:
        print("✅ 文件不存在错误处理正确")
    except Exception as e:
        print(f"✅ 文件不存在错误处理正确: {e}")
    
    return True


def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    # 清理单个测试文件
    test_files = [
        "test_frame_0.0s.png",
        "test_frame_1.0s.png", 
        "test_frame_2.5s.png",
        "test_first_frame.png",
        "test_first_frame.jpg",
        "test_first_frame.bmp"
    ]
    
    cleaned_count = 0
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✅ 已删除: {file}")
                cleaned_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
    
    # 清理测试目录
    test_dir = "test_multiple_frames"
    if os.path.exists(test_dir):
        try:
            import shutil
            shutil.rmtree(test_dir)
            print(f"✅ 已删除目录: {test_dir}")
            cleaned_count += 1
        except Exception as e:
            print(f"❌ 删除目录失败 {test_dir}: {e}")
    
    print(f"清理完成: {cleaned_count} 个文件/目录")


def main():
    """主测试函数"""
    print("视频第一帧提取工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("第一帧提取", test_first_frame_extraction),
        ("指定时间点提取", test_specific_time_extraction),
        ("多种格式输出", test_multiple_formats),
        ("批量帧提取", test_multiple_frames),
        ("视频信息获取", test_video_info),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 清理测试文件
    cleanup_test_files()
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查问题。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
