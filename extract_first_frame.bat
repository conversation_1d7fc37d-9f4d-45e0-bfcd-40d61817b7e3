@echo off
chcp 65001 >nul
echo 视频第一帧/指定帧提取工具
echo ========================

if "%~1"=="" (
    echo 用法: %0 ^<视频文件路径^> [选项]
    echo.
    echo 选项:
    echo   -o ^<输出路径^>     指定输出文件路径
    echo   -f ^<格式^>        指定输出格式 (png, jpg, bmp, tiff)
    echo   -t ^<时间^>        指定时间点(秒) (默认: 0.0 - 第一帧)
    echo.
    echo 示例:
    echo   %0 video.mp4
    echo   %0 video.mp4 -o first_frame.png
    echo   %0 video.mp4 -f jpg -t 2.5
    echo   %0 video.mp4 -o frame.jpg -f jpg -t 10.0
    echo.
    pause
    exit /b 1
)

set VIDEO_PATH=%~1
set OUTPUT_PATH=
set FORMAT=
set TIME=

:parse_args
shift
if "%~1"=="" goto execute
if "%~1"=="-o" (
    set OUTPUT_PATH=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-f" (
    set FORMAT=%~2
    shift
    shift
    goto parse_args
)
if "%~1"=="-t" (
    set TIME=%~2
    shift
    shift
    goto parse_args
)
shift
goto parse_args

:execute
set CMD=python get_first_frame.py "%VIDEO_PATH%"

if not "%OUTPUT_PATH%"=="" (
    set CMD=%CMD% -o "%OUTPUT_PATH%"
)

if not "%FORMAT%"=="" (
    set CMD=%CMD% -f "%FORMAT%"
)

if not "%TIME%"=="" (
    set CMD=%CMD% -t "%TIME%"
)

echo 执行命令: %CMD%
echo.
%CMD%

pause
