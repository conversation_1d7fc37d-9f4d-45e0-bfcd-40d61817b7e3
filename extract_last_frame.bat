@echo off
chcp 65001 >nul
echo 视频最后一帧提取工具
echo ==================

if "%~1"=="" (
    echo 用法: %0 ^<视频文件路径^> [输出路径] [格式]
    echo.
    echo 示例:
    echo   %0 video.mp4
    echo   %0 video.mp4 last_frame.png
    echo   %0 video.mp4 last_frame.jpg jpg
    echo.
    pause
    exit /b 1
)

set VIDEO_PATH=%~1
set OUTPUT_PATH=%~2
set FORMAT=%~3

if "%OUTPUT_PATH%"=="" (
    if "%FORMAT%"=="" (
        python get_last_frame.py "%VIDEO_PATH%"
    ) else (
        python get_last_frame.py "%VIDEO_PATH%" -f "%FORMAT%"
    )
) else (
    if "%FORMAT%"=="" (
        python get_last_frame.py "%VIDEO_PATH%" -o "%OUTPUT_PATH%"
    ) else (
        python get_last_frame.py "%VIDEO_PATH%" -o "%OUTPUT_PATH%" -f "%FORMAT%"
    )
)

pause
