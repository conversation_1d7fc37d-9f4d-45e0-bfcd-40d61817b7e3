# 视频第一帧/指定帧提取工具

基于Python + FFmpeg的视频帧提取工具，支持提取第一帧、指定时间点的帧以及批量提取多个帧。

## 功能特点

- 🎬 支持多种视频格式（MP4, AVI, MOV, MKV, WMV, FLV等）
- 🖼️ 支持多种输出图片格式（PNG, JPG, BMP, TIFF等）
- ⏰ 支持提取任意时间点的帧
- 📦 支持批量提取多个时间点的帧
- 📊 自动获取视频详细信息（时长、分辨率、帧率）
- 🛠️ 自动检测FFmpeg路径
- 💻 跨平台支持（Windows, Linux, macOS）
- 🔧 完善的错误处理和编码兼容性

## 环境要求

1. **Python 3.6+**
2. **FFmpeg** - 需要安装并添加到系统PATH中

### FFmpeg安装

#### Windows
1. 从 [FFmpeg官网](https://ffmpeg.org/download.html) 下载
2. 解压到任意目录（如 `C:\ffmpeg`）
3. 将 `C:\ffmpeg\bin` 添加到系统PATH环境变量

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install ffmpeg
```

#### macOS
```bash
brew install ffmpeg
```

## 使用方法

### 1. 命令行使用

#### 基本用法
```bash
# 提取第一帧，自动生成文件名
python get_first_frame.py video.mp4

# 指定输出文件
python get_first_frame.py video.mp4 -o first_frame.png

# 指定输出格式
python get_first_frame.py video.mp4 -f jpg

# 提取指定时间点的帧（2.5秒处）
python get_first_frame.py video.mp4 -t 2.5 -o frame_2_5s.jpg

# 批量提取多个时间点的帧
python get_first_frame.py video.mp4 --multiple 0 1.0 2.5 5.0 --output-dir frames

# 完整参数示例
python get_first_frame.py video.mp4 -o output/frame.jpg -f jpg -t 3.0
```

#### 参数说明
- `video_path`: 输入视频文件路径（必需）
- `-o, --output`: 输出图片路径（可选）
- `-f, --format`: 输出图片格式，支持 png, jpg, jpeg, bmp, tiff（默认: png）
- `-t, --time`: 提取指定时间点的帧（秒），默认为0.0（第一帧）
- `--multiple`: 提取多个时间点的帧（秒），例如: --multiple 0 1.5 3.0 5.2
- `--output-dir`: 批量提取时的输出目录

### 2. Python代码中使用

```python
from get_first_frame import VideoFirstFrameExtractor

# 创建提取器实例
extractor = VideoFirstFrameExtractor()

# 提取第一帧
output_path = extractor.extract_first_frame('video.mp4')
print(f"第一帧已保存到: {output_path}")

# 提取指定时间点的帧
output_path = extractor.extract_first_frame(
    'video.mp4', 
    'frame_2s.jpg', 
    'jpg',
    2.0  # 2秒处的帧
)

# 批量提取多个帧
frame_times = [0.0, 1.0, 2.5, 5.0]
output_paths = extractor.extract_multiple_frames(
    'video.mp4',
    frame_times,
    'output_dir',
    'png'
)

# 获取视频信息
video_info = extractor.get_video_info('video.mp4')
print(f"视频时长: {video_info['duration']:.2f}秒")
print(f"分辨率: {video_info['resolution']}")
print(f"帧率: {video_info['fps']:.2f} fps")
```

### 3. 使用示例

```python
# 运行示例脚本
python first_frame_examples.py
```

## 文件说明

- `get_first_frame.py` - 主要的提取工具类和命令行接口
- `first_frame_examples.py` - 使用示例和批量处理演示
- `test_first_frame.py` - 完整的功能测试脚本
- `README_first_frame.md` - 说明文档

## 支持的格式

### 输入视频格式
- MP4, AVI, MOV, MKV, WMV, FLV, 3GP, WEBM等

### 输出图片格式
- PNG (默认)
- JPG/JPEG
- BMP
- TIFF

## 功能特性

### 1. 智能时间点定位
- 精确定位到指定时间点的帧
- 自动处理视频开始和结束边界
- 支持毫秒级精度

### 2. 批量处理
- 一次性提取多个时间点的帧
- 自动生成有序的文件名
- 支持自定义输出目录

### 3. 视频信息获取
- 自动获取视频时长
- 解析视频分辨率
- 获取视频帧率信息
- 提供采样建议

### 4. 高质量输出
- 使用FFmpeg高质量设置
- 支持多种图片格式
- 保持原始分辨率

## 错误处理

工具包含完善的错误处理机制：

- 自动检测FFmpeg安装
- 验证输入文件存在性
- 处理编码兼容性问题
- 提供详细的错误信息和警告

## 使用场景

1. **视频预览图生成**: 快速生成视频缩略图
2. **关键帧提取**: 提取视频中的重要帧
3. **视频分析**: 分析视频内容变化
4. **批量处理**: 处理大量视频文件
5. **视频编辑辅助**: 为视频编辑提供参考帧

## 性能优化

- 使用FFmpeg的快速定位功能
- 避免重复解码整个视频
- 支持高分辨率视频处理
- 内存使用优化

## 常见问题

### Q: 提示"未找到FFmpeg"
A: 请确保已安装FFmpeg并添加到系统PATH环境变量中

### Q: 某些视频无法获取详细信息但能提取帧
A: 这是正常现象，工具会显示警告但仍能成功提取帧

### Q: 如何提取视频中间的帧
A: 使用 `-t` 参数指定时间点，例如 `-t 30.5` 提取30.5秒处的帧

### Q: 批量提取时如何控制文件命名
A: 工具会自动生成格式为 `视频名_frame_序号_时间点s.格式` 的文件名

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个工具！
