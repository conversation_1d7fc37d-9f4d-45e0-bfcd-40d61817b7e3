#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
获取视频最后一帧并保存的工具
基于Python + FFmpeg实现
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


class VideoLastFrameExtractor:
    """视频最后一帧提取器"""
    
    def __init__(self):
        self.ffmpeg_path = self._find_ffmpeg()
    
    def _find_ffmpeg(self):
        """查找FFmpeg可执行文件路径"""
        # 尝试在系统PATH中查找ffmpeg
        try:
            result = subprocess.run(['ffmpeg', '-version'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                return 'ffmpeg'
        except (subprocess.TimeoutExpired, FileNotFoundError):
            pass
        
        # 如果系统PATH中没有，尝试常见的安装路径
        common_paths = [
            'ffmpeg.exe',
            'C:/ffmpeg/bin/ffmpeg.exe',
            '/usr/bin/ffmpeg',
            '/usr/local/bin/ffmpeg'
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        raise FileNotFoundError("未找到FFmpeg，请确保已安装FFmpeg并添加到系统PATH中")
    
    def get_video_duration(self, video_path):
        """获取视频时长（秒）"""
        cmd = [
            self.ffmpeg_path,
            '-i', video_path,
            '-f', 'null',
            '-'
        ]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)
            # 从stderr中解析时长信息
            for line in result.stderr.split('\n'):
                if 'Duration:' in line:
                    duration_str = line.split('Duration:')[1].split(',')[0].strip()
                    # 解析时:分:秒.毫秒格式
                    time_parts = duration_str.split(':')
                    hours = float(time_parts[0])
                    minutes = float(time_parts[1])
                    seconds = float(time_parts[2])
                    total_seconds = hours * 3600 + minutes * 60 + seconds
                    return total_seconds
        except subprocess.TimeoutExpired:
            raise Exception("获取视频时长超时")
        except Exception as e:
            raise Exception(f"获取视频时长失败: {str(e)}")
        
        raise Exception("无法解析视频时长")
    
    def extract_last_frame(self, video_path, output_path=None, image_format='png'):
        """
        提取视频最后一帧
        
        Args:
            video_path (str): 输入视频文件路径
            output_path (str, optional): 输出图片路径，如果不指定则自动生成
            image_format (str): 输出图片格式，默认为png
        
        Returns:
            str: 输出图片的路径
        """
        # 验证输入文件
        if not os.path.exists(video_path):
            raise FileNotFoundError(f"视频文件不存在: {video_path}")
        
        # 生成输出路径
        if output_path is None:
            video_name = Path(video_path).stem
            output_path = f"{video_name}_last_frame.{image_format}"
        
        # 确保输出目录存在
        output_dir = os.path.dirname(output_path)
        if output_dir and not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        print(f"正在处理视频: {video_path}")
        print(f"输出路径: {output_path}")
        
        # 获取视频时长
        try:
            duration = self.get_video_duration(video_path)
            print(f"视频时长: {duration:.2f}秒")
            
            # 计算最后一帧的时间点（稍微提前一点以确保能获取到帧）
            last_frame_time = max(0, duration - 0.1)
            
        except Exception as e:
            print(f"警告: 无法获取精确时长，使用备用方法: {str(e)}")
            last_frame_time = None
        
        # 构建FFmpeg命令
        if last_frame_time is not None:
            # 方法1: 使用精确时间点
            cmd = [
                self.ffmpeg_path,
                '-i', video_path,
                '-ss', str(last_frame_time),
                '-vframes', '1',
                '-y',  # 覆盖输出文件
                output_path
            ]
        else:
            # 方法2: 使用反向查找最后一帧
            cmd = [
                self.ffmpeg_path,
                '-sseof', '-1',  # 从结尾开始
                '-i', video_path,
                '-vframes', '1',
                '-y',
                output_path
            ]
        
        try:
            print("正在提取最后一帧...")
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                if os.path.exists(output_path):
                    print(f"✓ 成功提取最后一帧: {output_path}")
                    return output_path
                else:
                    raise Exception("FFmpeg执行成功但未生成输出文件")
            else:
                raise Exception(f"FFmpeg执行失败: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            raise Exception("提取最后一帧超时")
        except Exception as e:
            raise Exception(f"提取最后一帧失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='获取视频最后一帧并保存')
    parser.add_argument('video_path', help='输入视频文件路径')
    parser.add_argument('-o', '--output', help='输出图片路径（可选）')
    parser.add_argument('-f', '--format', default='png', 
                       choices=['png', 'jpg', 'jpeg', 'bmp', 'tiff'],
                       help='输出图片格式（默认: png）')
    
    args = parser.parse_args()
    
    try:
        extractor = VideoLastFrameExtractor()
        output_path = extractor.extract_last_frame(
            args.video_path, 
            args.output, 
            args.format
        )
        print(f"\n✓ 处理完成！最后一帧已保存到: {output_path}")
        
    except Exception as e:
        print(f"\n✗ 错误: {str(e)}")
        sys.exit(1)


if __name__ == '__main__':
    main()
