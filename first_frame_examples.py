#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频第一帧提取工具使用示例
"""

from get_first_frame import VideoFirstFrameExtractor
import os


def example_first_frame():
    """提取第一帧示例"""
    print("=== 提取第一帧示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_path = "镜头1.mp4"
    
    if os.path.exists(video_path):
        try:
            # 提取第一帧，自动生成文件名
            output_path = extractor.extract_first_frame(video_path)
            print(f"成功提取第一帧: {output_path}")
            
            # 自定义输出路径和格式
            custom_output = extractor.extract_first_frame(
                video_path, 
                "custom_first_frame.jpg", 
                "jpg"
            )
            print(f"自定义输出: {custom_output}")
            
        except Exception as e:
            print(f"处理失败: {e}")
    else:
        print(f"视频文件不存在: {video_path}")


def example_specific_time():
    """提取指定时间点的帧示例"""
    print("\n=== 提取指定时间点的帧示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_path = "镜头1.mp4"
    
    if os.path.exists(video_path):
        try:
            # 提取不同时间点的帧
            time_points = [0.0, 1.0, 2.5, 4.0]
            
            for time_point in time_points:
                output_path = extractor.extract_first_frame(
                    video_path,
                    f"frame_at_{time_point:.1f}s.png",
                    "png",
                    time_point
                )
                print(f"✓ {time_point}秒处的帧: {output_path}")
                
        except Exception as e:
            print(f"处理失败: {e}")
    else:
        print(f"视频文件不存在: {video_path}")


def example_multiple_frames():
    """批量提取多个帧示例"""
    print("\n=== 批量提取多个帧示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_path = "镜头1.mp4"
    
    if os.path.exists(video_path):
        try:
            # 创建输出目录
            output_dir = "extracted_frames"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 定义要提取的时间点
            frame_times = [0.0, 1.0, 2.0, 3.0, 4.0, 5.0]
            
            # 批量提取
            output_paths = extractor.extract_multiple_frames(
                video_path,
                frame_times,
                output_dir,
                "jpg"
            )
            
            print(f"批量提取完成，共 {len(output_paths)} 个帧:")
            for path in output_paths:
                print(f"  - {path}")
                
        except Exception as e:
            print(f"批量处理失败: {e}")
    else:
        print(f"视频文件不存在: {video_path}")


def example_different_formats():
    """不同格式输出示例"""
    print("\n=== 不同格式输出示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_path = "镜头1.mp4"
    
    if os.path.exists(video_path):
        try:
            # 创建输出目录
            output_dir = "format_examples"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 不同格式的输出
            formats = ['png', 'jpg', 'bmp']
            
            for fmt in formats:
                output_path = os.path.join(output_dir, f"first_frame.{fmt}")
                result = extractor.extract_first_frame(video_path, output_path, fmt)
                
                # 获取文件大小
                file_size = os.path.getsize(result)
                print(f"✓ {fmt.upper()} 格式: {result} ({file_size:,} bytes)")
                
        except Exception as e:
            print(f"处理失败: {e}")


def example_video_info():
    """视频信息获取示例"""
    print("\n=== 视频信息获取示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    video_path = "镜头1.mp4"
    
    if os.path.exists(video_path):
        try:
            video_info = extractor.get_video_info(video_path)
            
            print(f"视频文件: {video_path}")
            print(f"时长: {video_info.get('duration', '未知'):.2f} 秒")
            print(f"分辨率: {video_info.get('resolution', '未知')}")
            print(f"帧率: {video_info.get('fps', '未知'):.2f} fps")
            
            # 根据视频时长计算建议的采样点
            if 'duration' in video_info:
                duration = video_info['duration']
                sample_count = min(10, int(duration))  # 最多10个采样点
                if sample_count > 1:
                    sample_times = [i * duration / (sample_count - 1) for i in range(sample_count)]
                    print(f"建议采样时间点: {[f'{t:.1f}s' for t in sample_times]}")
                
        except Exception as e:
            print(f"获取视频信息失败: {e}")


def example_batch_processing():
    """批量处理多个视频文件示例"""
    print("\n=== 批量处理多个视频文件示例 ===")
    
    extractor = VideoFirstFrameExtractor()
    
    # 查找当前目录中的所有视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
    video_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in video_extensions):
            video_files.append(file)
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    if video_files:
        # 创建输出目录
        output_dir = "batch_first_frames"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        success_count = 0
        for video_file in video_files:
            try:
                print(f"\n处理: {video_file}")
                
                # 为每个视频提取第一帧
                video_name = os.path.splitext(video_file)[0]
                output_path = os.path.join(output_dir, f"{video_name}_first_frame.png")
                
                result_path = extractor.extract_first_frame(video_file, output_path)
                print(f"✓ 成功: {result_path}")
                success_count += 1
                
            except Exception as e:
                print(f"✗ 失败: {e}")
        
        print(f"\n批量处理完成: {success_count}/{len(video_files)} 个文件成功处理")


if __name__ == '__main__':
    # 运行所有示例
    example_first_frame()
    example_specific_time()
    example_multiple_frames()
    example_different_formats()
    example_video_info()
    example_batch_processing()
