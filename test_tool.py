#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试脚本：验证视频最后一帧提取工具的所有功能
"""

import os
import sys
from get_last_frame import VideoLastFrameExtractor


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    extractor = VideoLastFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    try:
        # 测试基本提取
        output_path = extractor.extract_last_frame(video_file)
        if os.path.exists(output_path):
            print(f"✅ 基本提取成功: {output_path}")
            file_size = os.path.getsize(output_path)
            print(f"   文件大小: {file_size} bytes")
        else:
            print("❌ 基本提取失败：输出文件不存在")
            return False
            
    except Exception as e:
        print(f"❌ 基本提取异常: {e}")
        return False
    
    return True


def test_custom_output():
    """测试自定义输出"""
    print("\n=== 测试自定义输出 ===")
    
    extractor = VideoLastFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    # 测试不同格式
    formats = ['png', 'jpg', 'bmp']
    success_count = 0
    
    for fmt in formats:
        try:
            output_path = f"test_custom.{fmt}"
            result = extractor.extract_last_frame(video_file, output_path, fmt)
            
            if os.path.exists(result):
                file_size = os.path.getsize(result)
                print(f"✅ {fmt.upper()} 格式成功: {result} ({file_size} bytes)")
                success_count += 1
            else:
                print(f"❌ {fmt.upper()} 格式失败：文件不存在")
                
        except Exception as e:
            print(f"❌ {fmt.upper()} 格式异常: {e}")
    
    return success_count == len(formats)


def test_video_info():
    """测试视频信息获取"""
    print("\n=== 测试视频信息获取 ===")
    
    extractor = VideoLastFrameExtractor()
    video_file = "镜头1.mp4"
    
    if not os.path.exists(video_file):
        print(f"❌ 测试视频文件不存在: {video_file}")
        return False
    
    try:
        duration = extractor.get_video_duration(video_file)
        print(f"✅ 视频时长获取成功: {duration:.2f} 秒")
        
        if duration > 0:
            return True
        else:
            print("❌ 视频时长无效")
            return False
            
    except Exception as e:
        print(f"❌ 视频时长获取异常: {e}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    extractor = VideoLastFrameExtractor()
    
    # 测试不存在的文件
    try:
        extractor.extract_last_frame("nonexistent_video.mp4")
        print("❌ 错误处理失败：应该抛出异常")
        return False
    except FileNotFoundError:
        print("✅ 文件不存在错误处理正确")
    except Exception as e:
        print(f"✅ 文件不存在错误处理正确: {e}")
    
    return True


def cleanup_test_files():
    """清理测试文件"""
    print("\n=== 清理测试文件 ===")
    
    test_files = [
        "test_custom.png",
        "test_custom.jpg", 
        "test_custom.bmp"
    ]
    
    cleaned_count = 0
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✅ 已删除: {file}")
                cleaned_count += 1
            except Exception as e:
                print(f"❌ 删除失败 {file}: {e}")
    
    print(f"清理完成: {cleaned_count} 个文件")


def main():
    """主测试函数"""
    print("视频最后一帧提取工具 - 功能测试")
    print("=" * 50)
    
    tests = [
        ("基本功能", test_basic_functionality),
        ("自定义输出", test_custom_output),
        ("视频信息", test_video_info),
        ("错误处理", test_error_handling)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            if test_func():
                print(f"✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 清理测试文件
    cleanup_test_files()
    
    # 测试结果
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具运行正常。")
        return 0
    else:
        print("⚠️  部分测试失败，请检查问题。")
        return 1


if __name__ == '__main__':
    sys.exit(main())
