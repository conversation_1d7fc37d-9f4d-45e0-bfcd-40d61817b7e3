#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用示例：如何使用VideoLastFrameExtractor类
"""

from get_last_frame import VideoLastFrameExtractor
import os


def example_single_video():
    """单个视频处理示例"""
    print("=== 单个视频处理示例 ===")
    
    # 创建提取器实例
    extractor = VideoLastFrameExtractor()
    
    # 处理单个视频
    video_path = "镜头1.mp4"  # 使用当前目录中的视频文件
    
    if os.path.exists(video_path):
        try:
            # 提取最后一帧，自动生成输出文件名
            output_path = extractor.extract_last_frame(video_path)
            print(f"成功提取: {output_path}")
            
            # 也可以指定输出路径和格式
            custom_output = extractor.extract_last_frame(
                video_path, 
                "custom_last_frame.jpg", 
                "jpg"
            )
            print(f"自定义输出: {custom_output}")
            
        except Exception as e:
            print(f"处理失败: {e}")
    else:
        print(f"视频文件不存在: {video_path}")


def example_batch_processing():
    """批量处理示例"""
    print("\n=== 批量处理示例 ===")
    
    # 创建提取器实例
    extractor = VideoLastFrameExtractor()
    
    # 查找当前目录中的所有视频文件
    video_extensions = ['.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv']
    video_files = []
    
    for file in os.listdir('.'):
        if any(file.lower().endswith(ext) for ext in video_extensions):
            video_files.append(file)
    
    print(f"找到 {len(video_files)} 个视频文件")
    
    # 批量处理
    success_count = 0
    for video_file in video_files:
        try:
            print(f"\n处理: {video_file}")
            output_path = extractor.extract_last_frame(video_file)
            print(f"✓ 成功: {output_path}")
            success_count += 1
        except Exception as e:
            print(f"✗ 失败: {e}")
    
    print(f"\n批量处理完成: {success_count}/{len(video_files)} 个文件成功处理")


def example_with_custom_settings():
    """自定义设置示例"""
    print("\n=== 自定义设置示例 ===")
    
    extractor = VideoLastFrameExtractor()
    
    video_path = "镜头1.mp4"
    if os.path.exists(video_path):
        try:
            # 创建输出目录
            output_dir = "last_frames"
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)
            
            # 不同格式的输出
            formats = ['png', 'jpg', 'bmp']
            
            for fmt in formats:
                output_path = os.path.join(output_dir, f"last_frame.{fmt}")
                result = extractor.extract_last_frame(video_path, output_path, fmt)
                print(f"✓ 生成 {fmt} 格式: {result}")
                
        except Exception as e:
            print(f"处理失败: {e}")


if __name__ == '__main__':
    # 运行所有示例
    example_single_video()
    example_batch_processing()
    example_with_custom_settings()
